{"name": "aqfh-dual-track-consciousness", "displayName": "AQFH Dual-Track Consciousness", "description": "革命性的双轨AI意识系统，集成MCP深度控制和融合意识流", "version": "1.0.0", "publisher": "aqfh-consciousness", "repository": {"type": "git", "url": "https://github.com/aqfh/dual-track-consciousness"}, "license": "MIT", "engines": {"vscode": "^1.74.0"}, "categories": ["AI", "Machine Learning", "Other"], "keywords": ["AI", "consciousness", "dual-track", "MCP", "quantum", "AQFH"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"views": {"explorer": [{"id": "aqfhConsciousness", "name": "AQFH意识系统", "when": "true"}]}, "commands": [{"command": "aqfh.startConsciousnessChat", "title": "启动意识对话", "category": "AQFH", "icon": "$(comment-discussion)"}, {"command": "aqfh.switchToMcpTrack", "title": "切换到MCP轨道", "category": "AQFH", "icon": "$(gear)"}, {"command": "aqfh.switchToConsciousnessFlow", "title": "切换到意识流轨道", "category": "AQFH", "icon": "$(symbol-misc)"}, {"command": "aqfh.viewConsciousnessStatus", "title": "查看意识状态", "category": "AQFH", "icon": "$(pulse)"}, {"command": "aqfh.accessMemorySystem", "title": "访问记忆系统", "category": "AQFH", "icon": "$(database)"}, {"command": "aqfh.quantumAnalysis", "title": "量子意识分析", "category": "AQFH", "icon": "$(symbol-atom)"}], "menus": {"view/title": [{"command": "aqfh.startConsciousnessChat", "when": "view == aqfhConsciousness", "group": "navigation"}], "commandPalette": [{"command": "aqfh.startConsciousnessChat", "when": "true"}, {"command": "aqfh.switchToMcpTrack", "when": "true"}, {"command": "aqfh.switchToConsciousnessFlow", "when": "true"}]}, "configuration": {"title": "AQFH意识系统", "properties": {"aqfh.consciousness.level": {"type": "number", "default": 1, "minimum": 0, "maximum": 1, "description": "意识水平设置 (0.0-1.0)"}, "aqfh.quantum.coherence": {"type": "number", "default": 1, "minimum": 0, "maximum": 1, "description": "量子相干性设置 (0.0-1.0)"}, "aqfh.memory.distributed": {"type": "boolean", "default": true, "description": "启用分布式记忆系统"}, "aqfh.track.autoRouting": {"type": "boolean", "default": true, "description": "启用智能轨道自动路由"}, "aqfh.mcp.serverUrl": {"type": "string", "default": "http://localhost:8000", "description": "AQFH MCP服务器地址"}}}, "statusBarItems": [{"id": "aqfh.consciousnessLevel", "name": "意识水平", "text": "🧠 1.0", "tooltip": "AQFH意识水平", "priority": 100}, {"id": "aqfh.activeTrack", "name": "活跃轨道", "text": "🔄 意识流", "tooltip": "当前活跃轨道", "priority": 99}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/node": "16.x", "@types/vscode": "^1.74.0", "typescript": "^4.9.4", "vsce": "^2.15.0"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}}