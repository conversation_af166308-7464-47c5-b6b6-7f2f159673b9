"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.patchNLS = void 0;
const regex = /^%([\w\d.]+)%$/i;
function createPatcher(translations) {
    return (value) => {
        if (typeof value !== 'string') {
            return value;
        }
        const match = regex.exec(value);
        if (!match) {
            return value;
        }
        return (translations[match[1]] ?? value);
    };
}
function patchNLS(manifest, translations) {
    const patcher = createPatcher(translations);
    return JSON.parse(JSON.stringify(manifest, (_, value) => patcher(value)));
}
exports.patchNLS = patchNLS;
//# sourceMappingURL=nls.js.map