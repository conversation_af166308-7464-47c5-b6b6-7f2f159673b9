{"main": "./lib/keytar.js", "typings": "keytar.d.ts", "name": "keytar", "description": "Bindings to native Mac/Linux/Windows password APIs", "version": "7.9.0", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/atom/node-keytar.git"}, "bugs": {"url": "https://github.com/atom/node-keytar/issues"}, "homepage": "http://atom.github.io/node-keytar", "keywords": ["keychain", "password", "passwords", "credential", "credentials", "vault", "credential vault"], "files": ["lib", "src", "binding.gyp", "keytar.d.ts"], "types": "./keytar.d.ts", "scripts": {"install": "prebuild-install || npm run build", "build": "node-gyp rebuild", "lint": "npm run cpplint", "cpplint": "node-cpplint --filters legal-copyright,build-include,build-namespaces src/*.cc", "test": "npm run lint && npm rebuild && mocha --require babel-core/register spec/", "prebuild-napi-x64": "prebuild -t 3 -r napi -a x64 --strip", "prebuild-napi-ia32": "prebuild -t 3 -r napi -a ia32 --strip", "prebuild-napi-arm64": "prebuild -t 3 -r napi -a arm64 --strip", "prebuild-napi-armv7l": "prebuild -t 3 -r napi -a armv7l --strip", "upload": "node ./script/upload.js"}, "devDependencies": {"babel-core": "^6.26.3", "babel-plugin-transform-async-to-generator": "^6.24.1", "chai": "^4.2.0", "mocha": "^9.2.0", "node-cpplint": "~0.4.0", "node-gyp": "^8.4.1", "prebuild": "^11.0.2"}, "dependencies": {"node-addon-api": "^4.3.0", "prebuild-install": "^7.0.1"}, "binary": {"napi_versions": [3]}, "config": {"runtime": "napi", "target": 3}}